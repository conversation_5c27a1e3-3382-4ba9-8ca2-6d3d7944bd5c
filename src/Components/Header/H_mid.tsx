import React, { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import "./style.css";
import * as ROUTE from "../Constants/routes";
import {
  HEADER_HELPDESK,
  HEADER_INNOVATIONCALL,
  HEADER_OPPORTUNITES,
  HEADER_TECHNOLOGIES,
  HELPDESK,
  HOME,
  INNOVATION,
  OPPORTUNITY,
  TECHNOLOGY,
} from "../constants";
import { useDetectClickOutside } from "react-detect-click-outside";
import MultiColumnDropdown from "./MultiColumnDropdown";
import { mediaDropdownData, gtiServicesDropdownData } from "./dropdownData";

interface IHMid {
  handleShow?: any;
  show?: boolean;
}

const H_Mid: React.FC<IHMid> = ({ handleShow, show }) => {
  const location = useLocation();
  const [mediaDrop, setMediaDrop] = useState(false);
  const [premiumServicesDrop, setPremiumServicesDrop] = useState(false);
  const media_ref = useDetectClickOutside({
    onTriggered: () => setMediaDrop(false),
  });
  const premium_services_ref = useDetectClickOutside({
    onTriggered: () => setPremiumServicesDrop(false),
  });

  return (
    <div className="modern-nav-menu">
      <ul className="modern-nav-list">
        <li className="stagger-item">
          <Link
            className={`item micro-bounce focus-ring ${
              location.pathname === TECHNOLOGY ? "active" : ""
            }`}
            to={TECHNOLOGY}
            onClick={() => {
              if (show) handleShow();
            }}
          >
            {HEADER_TECHNOLOGIES}
          </Link>
        </li>
        <li>
          <Link
            className={`item ${
              location.pathname === OPPORTUNITY ? "active" : ""
            }`}
            onClick={() => {
              if (show) handleShow();
            }}
            to={OPPORTUNITY}
          >
            {HEADER_OPPORTUNITES}
          </Link>
        </li>
        <li>
          <Link
            className={`item ${
              location.pathname === INNOVATION ? "active" : ""
            }`}
            onClick={() => {
              if (show) handleShow();
            }}
            to={INNOVATION}
          >
            {HEADER_INNOVATIONCALL}
          </Link>
        </li>
        <li>
          <Link
            className={`item ${location.pathname === HELPDESK ? "active" : ""}`}
            onClick={() => {
              if (show) handleShow();
            }}
            to={HELPDESK}
          >
            {HEADER_HELPDESK}
          </Link>
        </li>
        {/* <li>
          <Link
            className={`item ${
              location.pathname === "/masterclass" ? "active" : ""
            }`}
            onClick={() => {
              if (show) handleShow();
            }}
            to="/masterclass"
          >
            Masterclass
          </Link>
        </li> */}
        <li ref={media_ref}>
          <button
            id="media_drop"
            onClick={() => {
              setMediaDrop(!mediaDrop);
            }}
            data-dropdown-toggle="dropdown"
            className="dropdown-button"
            type="button"
          >
            <Link
              className="text-sm font-medium text-gray-700 hover:text-GTI-BLUE-default transition-colors duration-200"
              to="/articles"
            >
              Media
            </Link>
            <svg
              className={`w-4 h-4 hidden md:block transition-transform duration-300 ${
                mediaDrop ? "rotate-180" : "rotate-0"
              } hover:scale-110`}
              aria-hidden="true"
              fill="none"
              stroke={location.pathname === HOME ? "black" : "white"}
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M19 9l-7 7-7-7"
              ></path>
            </svg>
          </button>
          <MultiColumnDropdown
            data={mediaDropdownData}
            isVisible={mediaDrop}
            onClose={() => setMediaDrop(false)}
            onItemClick={() => {
              setMediaDrop(false);
              if (show) handleShow();
            }}
          />
        </li>
        <li ref={premium_services_ref}>
          <button
            id="premium_services_drop"
            onClick={() => {
              setPremiumServicesDrop(!premiumServicesDrop);
            }}
            data-dropdown-toggle="dropdown"
            className="dropdown-button"
            type="button"
          >
            <Link
              className="text-sm font-medium text-gray-700 hover:text-GTI-BLUE-default transition-colors duration-200"
              to={ROUTE.PREMIUM_SERVICES}
            >
              GTI Services
            </Link>
            <svg
              className={`w-4 h-4 hidden md:block transition-transform duration-300 ${
                premiumServicesDrop ? "rotate-180" : "rotate-0"
              } hover:scale-110`}
              aria-hidden="true"
              fill="none"
              stroke={location.pathname === HOME ? "black" : "white"}
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M19 9l-7 7-7-7"
              ></path>
            </svg>
          </button>
          <MultiColumnDropdown
            data={gtiServicesDropdownData}
            isVisible={premiumServicesDrop}
            onClose={() => setPremiumServicesDrop(false)}
            onItemClick={() => {
              setPremiumServicesDrop(false);
              if (show) handleShow();
            }}
          />
        </li>
      </ul>
    </div>
  );
};

export default H_Mid;
