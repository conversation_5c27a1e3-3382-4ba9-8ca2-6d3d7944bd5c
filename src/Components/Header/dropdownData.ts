export interface DropdownItem {
  title: string;
  description: string;
  link: string;
  icon?: string;
}

export interface DropdownSection {
  sectionTitle: string;
  sectionIcon?: string;
  items: DropdownItem[];
}

export interface MultiColumnDropdownData {
  sections: DropdownSection[];
}

// GTI Services Dropdown Data
export const gtiServicesDropdownData: MultiColumnDropdownData = {
  sections: [
    {
      sectionTitle: "Solutions",
      sectionIcon: "🔧",
      items: [
        {
          title: "Technology Showcase Platform",
          description: "Create a unique environment to initiate and foster collaboration",
          link: "/technology",
          icon: "🚀"
        },
        {
          title: "Market Access Services",
          description: "Comprehensive support for technology deployment and validation",
          link: "/displayer-services",
          icon: "🌍"
        },
        {
          title: "Innovation Scouting",
          description: "Customized assistance for finding technology partners",
          link: "/scouter-services",
          icon: "🔍"
        }
      ]
    },
    {
      sectionTitle: "Service Types",
      sectionIcon: "⚙️",
      items: [
        {
          title: "For Technology Displayers",
          description: "Support for technology companies and startups to deploy solutions",
          link: "/displayer-services",
          icon: "📈"
        },
        {
          title: "For Technology Scouters",
          description: "Connect startups and seekers with potential investors and partners",
          link: "/scouter-services",
          icon: "🎯"
        },
        {
          title: "Premium Services",
          description: "Additional visibility for participants who require extra limelight",
          link: "/premium-services",
          icon: "⭐"
        }
      ]
    },
    {
      sectionTitle: "Use Cases",
      sectionIcon: "💼",
      items: [
        {
          title: "Innovation Partnerships",
          description: "Match SMEs, MNEs and research institutes and help them create consortiums",
          link: "/innovation",
          icon: "🤝"
        },
        {
          title: "Technology Transfer",
          description: "Connect startups and seekers with potential investors and industry experts",
          link: "/opportunities",
          icon: "🔄"
        },
        {
          title: "Market Opportunities",
          description: "Bring together ideal applicants and recruiters to increase interview numbers",
          link: "/opportunities",
          icon: "📊"
        }
      ]
    },
    {
      sectionTitle: "Value Adds",
      sectionIcon: "💎",
      items: [
        {
          title: "Global Network Access",
          description: "Generate meaningful connections and value adding meetings",
          link: "/premium-services",
          icon: "🌐"
        },
        {
          title: "Expert Consultation",
          description: "Offer your participants an engaging mobile event app built for networking",
          link: "/helpdesk",
          icon: "👨‍💼"
        },
        {
          title: "AI-powered Matchmaking",
          description: "Increase participant engagement on your networking event with AI",
          link: "/premium-services",
          icon: "🤖"
        }
      ]
    }
  ]
};

// Media Dropdown Data
export const mediaDropdownData: MultiColumnDropdownData = {
  sections: [
    {
      sectionTitle: "Content Hub",
      sectionIcon: "📚",
      items: [
        {
          title: "Articles",
          description: "In-depth analysis and insights on technology trends",
          link: "/articles",
          icon: "📰"
        },
        {
          title: "Publications",
          description: "Research papers and technical documentation",
          link: "/publications",
          icon: "📄"
        }
      ]
    },
    {
      sectionTitle: "News & Events",
      sectionIcon: "📢",
      items: [
        {
          title: "Latest News",
          description: "Stay updated with the latest technology and innovation news",
          link: "/news",
          icon: "🗞️"
        },
        {
          title: "Events",
          description: "Join our curated events for networking and knowledge sharing",
          link: "/events",
          icon: "🎯"
        }
      ]
    }
  ]
};
