import React from "react";
import { Link } from "react-router-dom";
import {
  MultiColumnDropdownData,
  DropdownSection,
  DropdownItem,
} from "./dropdownData";

interface MultiColumnDropdownProps {
  data: MultiColumnDropdownData;
  isVisible: boolean;
  onClose: () => void;
  onItemClick: () => void;
}

const MultiColumnDropdown: React.FC<MultiColumnDropdownProps> = ({
  data,
  isVisible,
  onClose,
  onItemClick,
}) => {
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Escape") {
      onClose();
    }
  };
  return (
    <div
      className={`multi-column-dropdown ${isVisible ? "show" : "hide"}`}
      onKeyDown={handleKeyDown}
      role="menu"
      aria-hidden={!isVisible}
    >
      <div className="multi-column-dropdown-container">
        <div className="multi-column-grid">
          {data.sections.map(
            (section: DropdownSection, sectionIndex: number) => (
              <div
                key={sectionIndex}
                className="dropdown-section"
                style={{ animationDelay: `${sectionIndex * 0.1}s` }}
              >
                <div className="section-header">
                  {section.sectionIcon && (
                    <span className="section-icon">{section.sectionIcon}</span>
                  )}
                  <h3 className="section-title">{section.sectionTitle}</h3>
                </div>
                <div className="section-items">
                  {section.items.map(
                    (item: DropdownItem, itemIndex: number) => (
                      <Link
                        key={itemIndex}
                        to={item.link}
                        className="dropdown-item-link"
                        onClick={onItemClick}
                        style={{
                          animationDelay: `${
                            (sectionIndex * section.items.length + itemIndex) *
                            0.05
                          }s`,
                        }}
                        role="menuitem"
                        tabIndex={isVisible ? 0 : -1}
                      >
                        <div className="dropdown-item-content">
                          {item.icon && (
                            <div className="item-icon-wrapper">
                              <span className="item-icon">{item.icon}</span>
                            </div>
                          )}
                          <div className="item-text-content">
                            <h4 className="item-title">{item.title}</h4>
                            <p className="item-description">
                              {item.description}
                            </p>
                          </div>
                        </div>
                      </Link>
                    )
                  )}
                </div>
              </div>
            )
          )}
        </div>

        {/* Bottom section with "See all" link */}
        <div className="dropdown-footer">
          <div className="footer-links">
            <Link
              to="/premium-services"
              className="see-all-link"
              onClick={onItemClick}
            >
              See all →
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MultiColumnDropdown;
