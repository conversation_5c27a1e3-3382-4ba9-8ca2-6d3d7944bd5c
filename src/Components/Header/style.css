/* Modern Navbar Container */
.modern-navbar-container {
  @apply fixed top-0 left-0 right-0 z-50 flex items-center justify-between w-full px-3 sm:px-4 lg:px-8;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  height: 60px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Mobile-specific navbar adjustments */
@media (max-width: 640px) {
  .modern-navbar-container {
    height: 56px;
    @apply px-3;
  }
}

.modern-navbar-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.1) 0%,
    rgba(147, 51, 234, 0.05) 100%
  );
  pointer-events: none;
}

.modern-navbar-content {
  @apply flex items-center justify-between flex-1 ml-4;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-navbar-content.mobile-closed {
  @apply hidden lg:flex;
}

.modern-navbar-content.mobile-open {
  @apply flex flex-col lg:flex-row absolute lg:relative top-[60px] lg:top-0 left-0 right-0 bg-white lg:bg-transparent backdrop-blur-xl lg:backdrop-blur-none p-4 sm:p-6 lg:p-0 shadow-2xl lg:shadow-none z-50;
  background-color: rgba(255, 255, 255, 0.98);
  border-top: 1px solid rgba(59, 130, 246, 0.2);
  animation: slideDown 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  max-height: calc(100vh - 60px);
  overflow-y: auto;
}

/* Mobile-specific mobile menu adjustments */
@media (max-width: 640px) {
  .modern-navbar-content.mobile-open {
    top: 56px;
    max-height: calc(100vh - 56px);
    @apply p-4;
  }
}

.modern-navbar-overlay {
  @apply fixed inset-0 bg-black/20 backdrop-blur-sm z-40 lg:hidden;
  top: 60px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Mobile-specific overlay adjustments */
@media (max-width: 640px) {
  .modern-navbar-overlay {
    top: 56px;
  }
}

/* Modern Navigation Items */
.item {
  @apply relative px-3 py-2 mx-1 text-sm font-medium text-gray-700 rounded-xl transition-all duration-300 ease-out;
  font-family: "Inter", "Plus Jakarta Sans", sans-serif;
  letter-spacing: -0.01em;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.item::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
  border-radius: 2px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(-50%);
}

.item::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.1),
    rgba(147, 51, 234, 0.05)
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 12px;
}

.item:hover::before {
  width: 90%;
}

.item:hover::after {
  opacity: 1;
}

.item:hover {
  @apply text-GTI-BLUE-default transform -translate-y-1 shadow-lg;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

.item:active {
  transform: translateY(0) scale(0.98);
}

/* Active Navigation Item */
.item.active {
  @apply text-GTI-BLUE-default bg-white shadow-md;
}

.item.active::before {
  width: 90%;
}

.item.active::after {
  opacity: 0.7;
}

/* Modern Navigation Menu */
.modern-nav-menu {
  @apply flex justify-center items-center flex-1;
}

.modern-nav-list {
  @apply flex items-center space-x-2 lg:space-x-4;
  flex-wrap: wrap;
}

.modern-nav-list li {
  @apply relative;
}

/* Dropdown Button Styles */
.dropdown-button {
  @apply inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-xl transition-all duration-300 ease-out;
  font-family: "Inter", "Plus Jakarta Sans", sans-serif;
  letter-spacing: -0.01em;
  white-space: nowrap;
}

.dropdown-button:hover {
  @apply text-GTI-BLUE-default transform -translate-y-1;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

/* Modern Dropdown Styles */
.modern-dropdown {
  @apply absolute top-full left-1/2 transform -translate-x-1/2 mt-3 w-64 z-50;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-dropdown.show {
  @apply opacity-100 visible scale-100;
}

.modern-dropdown.hide {
  @apply opacity-0 invisible scale-95 pointer-events-none;
}

.modern-dropdown-menu {
  @apply bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-gray-200/50 overflow-hidden;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95),
    rgba(248, 250, 252, 0.95)
  );
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.modern-dropdown-item-wrapper {
  @apply border-b border-gray-100/50 last:border-b-0;
}

.modern-dropdown-item {
  @apply flex items-center w-full px-6 py-4 text-left transition-all duration-300;
  font-family: "Inter", sans-serif;
  text-decoration: none;
}

.modern-dropdown-item-wrapper:hover .modern-dropdown-item {
  @apply bg-gradient-to-r from-blue-50 to-indigo-50 transform translate-x-1;
}

.dropdown-icon {
  @apply text-xl mr-4 transition-transform duration-300;
  filter: grayscale(0.3);
}

.modern-dropdown-item:hover .dropdown-icon {
  filter: grayscale(0);
  transform: scale(1.1);
}

.dropdown-text {
  @apply text-gray-700 font-medium text-sm transition-colors duration-300;
  font-family: "Inter", sans-serif;
  letter-spacing: -0.01em;
}

.modern-dropdown-item:hover .dropdown-text {
  @apply text-GTI-BLUE-default;
}

/* Multi-Column Dropdown Styles */
.multi-column-dropdown {
  @apply absolute top-full left-0 mt-3 z-50;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.multi-column-dropdown.show {
  @apply opacity-100 visible scale-100;
}

.multi-column-dropdown.hide {
  @apply opacity-0 invisible scale-95 pointer-events-none;
}

.multi-column-dropdown-container {
  @apply bg-white/95 backdrop-blur-xl shadow-2xl border-t border-gray-200/50 overflow-hidden mx-auto;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95),
    rgba(248, 250, 252, 0.95)
  );
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 1200px;
  border-radius: 0 0 1rem 1rem;
}

.multi-column-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-0;
  min-height: 400px;
}

.dropdown-section {
  @apply p-6 border-r border-gray-100/50 last:border-r-0;
  animation: sectionSlideIn 0.5s ease-out both;
}

.dropdown-section:nth-child(even) {
  @apply bg-gray-50/30;
}

.section-header {
  @apply flex items-center mb-4 pb-3 border-b border-gray-200/50;
}

.section-icon {
  @apply text-xl mr-3;
  filter: grayscale(0.2);
}

.section-title {
  @apply text-sm font-semibold text-gray-800 uppercase tracking-wide;
  font-family: "Inter", sans-serif;
  letter-spacing: 0.05em;
}

.section-items {
  @apply space-y-3;
}

.dropdown-item-link {
  @apply block p-3 rounded-xl transition-all duration-300 hover:bg-white/80 hover:shadow-md;
  text-decoration: none;
  animation: itemFadeIn 0.4s ease-out both;
}

.dropdown-item-content {
  @apply flex items-start space-x-3;
}

.item-icon-wrapper {
  @apply flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-lg bg-gradient-to-br from-blue-50 to-indigo-50;
  transition: all 0.3s ease;
}

.item-icon {
  @apply text-lg;
  filter: grayscale(0.3);
  transition: all 0.3s ease;
}

.dropdown-item-link:hover .item-icon-wrapper {
  @apply bg-gradient-to-br from-blue-100 to-indigo-100 transform scale-110;
}

.dropdown-item-link:hover .item-icon {
  filter: grayscale(0);
  transform: scale(1.1);
}

.item-text-content {
  @apply flex-1 min-w-0;
}

.item-title {
  @apply text-sm font-medium text-gray-900 mb-1 transition-colors duration-300;
  font-family: "Inter", sans-serif;
  line-height: 1.3;
}

.item-description {
  @apply text-xs text-gray-600 leading-relaxed transition-colors duration-300;
  font-family: "Inter", sans-serif;
  line-height: 1.4;
}

.dropdown-item-link:hover .item-title {
  @apply text-GTI-BLUE-default;
}

.dropdown-item-link:hover .item-description {
  @apply text-gray-700;
}

.dropdown-footer {
  @apply border-t border-gray-200/50 bg-gray-50/50 px-6 py-4;
}

.footer-links {
  @apply flex justify-end;
}

.see-all-link {
  @apply text-sm font-medium text-GTI-BLUE-default hover:text-blue-700 transition-colors duration-300;
  font-family: "Inter", sans-serif;
  text-decoration: none;
}

.see-all-link:hover {
  transform: translateX(4px);
  text-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

/* Smooth entrance animation for dropdown footer */
.dropdown-footer {
  animation: footerSlideUp 0.5s ease-out 0.4s both;
}

@keyframes footerSlideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Pulse effect for section icons */
.section-icon {
  animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Subtle glow effect on hover */
.multi-column-dropdown-container:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(59, 130, 246, 0.1);
}

/* Dropdown Animation Effects */
@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes sectionSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes itemFadeIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.modern-dropdown.show .modern-dropdown-menu {
  animation: dropdownSlideIn 0.3s ease-out;
}

/* Stagger animation for dropdown items */
.modern-dropdown-item-wrapper:nth-child(1) .modern-dropdown-item {
  animation-delay: 0.05s;
}

.modern-dropdown-item-wrapper:nth-child(2) .modern-dropdown-item {
  animation-delay: 0.1s;
}

.modern-dropdown-item-wrapper:nth-child(3) .modern-dropdown-item {
  animation-delay: 0.15s;
}

.modern-dropdown-item-wrapper:nth-child(4) .modern-dropdown-item {
  animation-delay: 0.2s;
}

@keyframes itemSlideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.modern-dropdown.show .modern-dropdown-item {
  animation: itemSlideIn 0.3s ease-out both;
}

/* Enhanced Multi-Column Dropdown Animations */
.multi-column-dropdown.show .multi-column-dropdown-container {
  animation: dropdownSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.multi-column-dropdown.show .dropdown-section {
  animation: sectionSlideIn 0.5s ease-out both;
}

.multi-column-dropdown.show .dropdown-item-link {
  animation: itemFadeIn 0.4s ease-out both;
}

/* Enhanced hover effects for multi-column dropdown */
.dropdown-item-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.dropdown-item-link:hover .item-icon-wrapper {
  transform: scale(1.15) rotate(5deg);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.section-header:hover .section-icon {
  transform: scale(1.2) rotate(10deg);
  filter: grayscale(0);
}

/* Stagger animations for sections */
.dropdown-section:nth-child(1) {
  animation-delay: 0s;
}

.dropdown-section:nth-child(2) {
  animation-delay: 0.1s;
}

.dropdown-section:nth-child(3) {
  animation-delay: 0.2s;
}

.dropdown-section:nth-child(4) {
  animation-delay: 0.3s;
}

/* Stagger animations for items within sections */
.dropdown-item-link:nth-child(1) {
  animation-delay: 0.1s;
}

.dropdown-item-link:nth-child(2) {
  animation-delay: 0.15s;
}

.dropdown-item-link:nth-child(3) {
  animation-delay: 0.2s;
}

.dropdown-item-link:nth-child(4) {
  animation-delay: 0.25s;
}

.item2 {
  @apply flex flex-row items-center lg:mx-3 px-3 py-2 rounded-xl transition-all duration-300;
}

.item2:hover {
  @apply bg-white/10 backdrop-blur-sm transform scale-105;
}

/* Enhanced Modal Buttons */
.signModalActive {
  @apply text-GTI-BLUE-default bg-white border-2 border-GTI-BLUE-default rounded-xl px-6 py-3 font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-lg;
}

.signModal {
  @apply bg-GTI-BLUE-default text-white rounded-xl px-6 py-3 font-semibold transition-all duration-300 transform hover:scale-105 hover:bg-blue-700 hover:shadow-xl;
  position: relative;
  overflow: hidden;
}

.signModal::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.signModal:hover::before {
  left: 100%;
}

/* Dropdown Animations */
.dropdown-menu {
  @apply opacity-0 invisible transform scale-95 transition-all duration-200 ease-out;
}

.dropdown-menu.show {
  @apply opacity-100 visible transform scale-100;
}

.dropdown-item {
  @apply transition-all duration-200 ease-out transform hover:translate-x-2 hover:bg-blue-50;
}

/* Enhanced Mobile Navigation */

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modern-nav-list {
  @apply flex items-center space-x-2 lg:space-x-4;
  flex-wrap: wrap;
}

/* Mobile Navigation List */
@media (max-width: 1023px) {
  .modern-nav-list {
    @apply flex-col space-x-0 space-y-3 w-full;
  }

  .modern-nav-list li {
    @apply w-full;
  }

  .item {
    @apply w-full text-center py-4 px-6 text-base font-medium rounded-xl;
    -webkit-tap-highlight-color: transparent;
    min-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .dropdown-button {
    @apply w-full text-center py-4 px-6 text-base font-medium rounded-xl;
    -webkit-tap-highlight-color: transparent;
    min-height: 48px;
  }

  .modern-dropdown {
    @apply relative top-0 left-0 transform-none mt-2 w-full;
  }

  .modern-dropdown-menu {
    @apply shadow-lg border border-gray-200 rounded-xl;
  }

  .modern-dropdown-item {
    @apply px-6 py-4 text-base rounded-xl;
    -webkit-tap-highlight-color: transparent;
    min-height: 48px;
  }

  .dropdown-icon {
    @apply text-xl mr-4;
  }

  /* Multi-column dropdown mobile styles */
  .multi-column-dropdown {
    @apply relative top-0 left-0 transform-none mt-2 w-full;
    max-width: none;
    margin-left: 0;
  }

  .multi-column-dropdown-container {
    max-width: none;
    border-radius: 0.75rem;
  }

  .multi-column-grid {
    @apply grid-cols-1 gap-0;
    min-height: auto;
  }

  .dropdown-section {
    @apply border-r-0 border-b border-gray-100/50 last:border-b-0 p-4;
  }

  .dropdown-section:nth-child(even) {
    @apply bg-transparent;
  }

  .section-header {
    @apply mb-3 pb-2;
  }

  .section-title {
    @apply text-base;
  }

  .dropdown-item-link {
    @apply p-4 text-base;
    min-height: 48px;
  }

  .item-title {
    @apply text-base font-medium;
  }

  .item-description {
    @apply text-sm;
  }
}

/* Extra mobile adjustments for very small screens */
@media (max-width: 640px) {
  .modern-nav-list {
    @apply space-y-2;
  }

  .item {
    @apply py-3 px-4 text-sm;
    min-height: 44px;
  }

  .dropdown-button {
    @apply py-3 px-4 text-sm;
    min-height: 44px;
  }

  .modern-dropdown-item {
    @apply px-4 py-3 text-sm;
    min-height: 44px;
  }

  /* Multi-column dropdown small mobile styles */
  .dropdown-section {
    @apply p-3;
  }

  .dropdown-item-link {
    @apply p-3 text-sm;
    min-height: 44px;
  }

  .item-title {
    @apply text-sm font-medium;
  }

  .item-description {
    @apply text-xs;
  }

  .section-title {
    @apply text-sm;
  }

  /* Disable animations on mobile for better performance */
  .section-icon {
    animation: none;
  }

  .dropdown-footer {
    animation: none;
  }

  .multi-column-dropdown.show .dropdown-section,
  .multi-column-dropdown.show .dropdown-item-link {
    animation: none;
  }
}

/* Tablet responsive styles */
@media (max-width: 768px) and (min-width: 641px) {
  .multi-column-grid {
    @apply grid-cols-2;
  }

  .dropdown-section {
    @apply p-5;
  }

  .dropdown-item-link {
    @apply p-3;
  }

  .item-title {
    @apply text-sm;
  }

  .item-description {
    @apply text-xs;
  }
}

/* Mobile Menu Animations */
.mobile-menu {
  @apply transform transition-all duration-300 ease-in-out;
}

.mobile-menu.open {
  @apply translate-x-0;
}

.mobile-menu.closed {
  @apply -translate-x-full;
}

/* Modern Logo Container */
.modern-logo-container {
  @apply flex items-center justify-between relative z-10;
}

.logo-wrapper {
  @apply cursor-pointer transition-all duration-300 ease-out;
}

.logo-wrapper:hover {
  @apply transform scale-105;
}

.modern-logo {
  @apply h-10 w-auto object-contain transition-all duration-300;
  max-width: 140px;
  min-height: 32px;
}

.modern-logo:hover {
  transform: scale(1.05);
}

/* Mobile logo adjustments */
@media (max-width: 640px) {
  .modern-logo {
    @apply h-8;
    max-width: 120px;
    min-height: 28px;
  }
}

/* Modern Mobile Toggle */
.modern-mobile-toggle {
  @apply lg:hidden flex flex-col justify-center items-center w-10 h-10 bg-white/90 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200/50 transition-all duration-300 hover:shadow-xl hover:scale-105;
  -webkit-tap-highlight-color: transparent;
}

.hamburger-line {
  @apply w-5 h-0.5 bg-GTI-BLUE-default transition-all duration-300 ease-in-out;
  margin: 2px 0;
}

.modern-mobile-toggle:hover .hamburger-line {
  @apply bg-blue-600;
}

/* Mobile hamburger adjustments */
@media (max-width: 640px) {
  .modern-mobile-toggle {
    @apply w-9 h-9;
  }

  .hamburger-line {
    @apply w-4;
  }
}

/* Modern User Actions */
.modern-user-actions {
  @apply flex items-center justify-end;
}

.modern-user-menu {
  @apply flex items-center space-x-4;
}

.modern-notification-button {
  @apply relative p-2 rounded-xl bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-all duration-300 hover:scale-105;
}

.modern-notification-icon {
  @apply w-6 h-6 text-gray-700 hover:text-GTI-BLUE-default transition-colors duration-200;
}

.modern-notification-badge {
  @apply absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full min-w-[18px] h-[18px] flex items-center justify-center;
  font-size: 10px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.modern-profile-button {
  @apply flex items-center space-x-3 px-4 py-2 rounded-xl bg-white/10 backdrop-blur-sm hover:bg-white/20 transition-all duration-300 hover:scale-105 max-w-[200px];
}

.modern-profile-avatar {
  @apply w-8 h-8 rounded-full object-cover border-2 border-white/50 hover:border-GTI-BLUE-default transition-colors duration-200;
}

.modern-profile-name {
  @apply text-gray-700 font-medium truncate text-sm;
  font-family: "Inter", sans-serif;
}

/* Modern Get Started Button */
.modern-get-started-button {
  @apply px-6 py-2.5 text-sm font-semibold text-white bg-GTI-BLUE-default rounded-xl transition-all duration-300 hover:bg-blue-600 hover:scale-105 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-GTI-BLUE-default focus:ring-offset-2;
  font-family: "Inter", sans-serif;
  letter-spacing: -0.01em;
}

.modern-get-started-button:active {
  transform: scale(0.98);
}

/* Mobile User Actions */
@media (max-width: 1023px) {
  .modern-user-menu {
    @apply flex-col space-x-0 space-y-4 w-full mt-4;
  }

  .modern-profile-button {
    @apply w-full justify-center max-w-none py-4 rounded-xl;
    -webkit-tap-highlight-color: transparent;
    min-height: 48px;
  }

  .modern-notification-button {
    @apply w-full justify-center py-4 rounded-xl;
    -webkit-tap-highlight-color: transparent;
    min-height: 48px;
  }

  .modern-get-started-button {
    @apply w-full py-4 text-base font-semibold rounded-xl;
    -webkit-tap-highlight-color: transparent;
    min-height: 48px;
  }
}

/* Extra mobile adjustments for user actions */
@media (max-width: 640px) {
  .modern-user-menu {
    @apply space-y-3 mt-3;
  }

  .modern-profile-button {
    @apply py-3 text-sm;
    min-height: 44px;
  }

  .modern-notification-button {
    @apply py-3;
    min-height: 44px;
  }

  .modern-get-started-button {
    @apply py-3 text-sm;
    min-height: 44px;
  }
}

/* Legacy Logo Animation (for backward compatibility) */
.logo-container {
  @apply transition-all duration-300 ease-out;
}

.logo-container:hover {
  @apply transform scale-110 rotate-3;
}

/* Modern Visual Effects and Animations */

/* Glassmorphism Effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Floating Animation */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

/* Glow Effect */
.glow-effect {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  transition: box-shadow 0.3s ease;
}

.glow-effect:hover {
  box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
}

/* Shimmer Effect */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.shimmer-effect {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Smooth Scroll Navbar */
.navbar-scrolled {
  @apply shadow-2xl;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
}

/* Micro Interactions */
.micro-bounce {
  transition: transform 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.micro-bounce:active {
  transform: scale(0.95);
}

/* Gradient Text Effect */
.gradient-text {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6, #06b6d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Morphing Border */
.morphing-border {
  position: relative;
  overflow: hidden;
}

.morphing-border::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.3),
    transparent
  );
  transition: left 0.5s;
}

.morphing-border:hover::before {
  left: 100%;
}

/* Enhanced Focus States */
.focus-ring:focus {
  @apply outline-none ring-2 ring-GTI-BLUE-default ring-offset-2 ring-offset-white;
}

/* Mobile-specific improvements */
@media (max-width: 640px) {
  /* Prevent zoom on input focus on iOS */
  input,
  select,
  textarea {
    font-size: 16px !important;
  }

  /* Improve touch targets */
  button,
  a,
  [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }

  /* Smooth scrolling for mobile menu */
  .modern-navbar-content.mobile-open {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Prevent horizontal scroll */
  .modern-navbar-container {
    overflow-x: hidden;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .item:hover {
    @apply transform-none;
  }

  .modern-mobile-toggle:hover {
    @apply transform-none;
  }

  .logo-wrapper:hover {
    @apply transform-none;
  }
}

/* Stagger Animation for Menu Items */
.stagger-item {
  opacity: 0;
  transform: translateY(20px);
  animation: staggerIn 0.5s ease-out forwards;
}

.stagger-item:nth-child(1) {
  animation-delay: 0.1s;
}
.stagger-item:nth-child(2) {
  animation-delay: 0.2s;
}
.stagger-item:nth-child(3) {
  animation-delay: 0.3s;
}
.stagger-item:nth-child(4) {
  animation-delay: 0.4s;
}
.stagger-item:nth-child(5) {
  animation-delay: 0.5s;
}

@keyframes staggerIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Parallax Effect */
.parallax-element {
  transform: translateZ(0);
  will-change: transform;
}

/* Modern Scrollbar */
.modern-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.modern-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.modern-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 3px;
}

.modern-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #2563eb, #7c3aed);
}
